"""
数据库模型定义
"""
from sqlalchemy import Column, Integer, String, Text, DECIMAL, DateTime, Date, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base


class Product(Base):
    """产品模型"""
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(String(50), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    name_en = Column(String(255))
    name_cn = Column(String(255))
    price = Column(DECIMAL(10, 2), nullable=False)
    category = Column(String(100))
    description = Column(Text)
    aliases = Column(ARRAY(String))  # 产品别名数组
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    order_items = relationship("OrderItem", back_populates="product")


class Order(Base):
    """订单模型"""
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    customer_name = Column(String(255))
    order_date = Column(Date)
    status = Column(String(50), default="pending")  # pending, completed, failed
    total_amount = Column(DECIMAL(10, 2))
    original_file_path = Column(String(500))
    processed_data = Column(JSONB)  # 存储原始处理数据
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")


class OrderItem(Base):
    """订单项模型"""
    __tablename__ = "order_items"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    product_id = Column(String(50), ForeignKey("products.product_id"))
    matched_name = Column(String(255))
    original_input = Column(String(255))  # 客户原始输入
    quantity = Column(Integer, nullable=False)
    unit_price = Column(DECIMAL(10, 2))
    total_price = Column(DECIMAL(10, 2))
    match_score = Column(DECIMAL(3, 2))  # 匹配置信度分数
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    order = relationship("Order", back_populates="items")
    product = relationship("Product", back_populates="order_items")
