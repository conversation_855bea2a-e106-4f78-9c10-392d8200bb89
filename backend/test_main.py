"""
简单的测试脚本
"""
import pytest
import asyncio
from pathlib import Path
import sys

# 添加项目根目录到 Python 路径
sys.path.append(str(Path(__file__).parent))

from services.ocr_service import OCRService
from services.llm_service import LLMService
from services.fuzzy_matching_service import FuzzyMatchingService


def test_ocr_service_init():
    """测试 OCR 服务初始化"""
    try:
        ocr_service = OCRService()
        assert ocr_service.reader is not None
        print("✅ OCR 服务初始化成功")
    except Exception as e:
        print(f"❌ OCR 服务初始化失败: {e}")


def test_llm_service_init():
    """测试 LLM 服务初始化"""
    try:
        llm_service = LLMService()
        print("✅ LLM 服务初始化成功")
    except Exception as e:
        print(f"❌ LLM 服务初始化失败: {e}")


def test_fuzzy_matching_service():
    """测试模糊匹配服务"""
    try:
        fuzzy_service = FuzzyMatchingService()
        
        # 模拟产品数据
        class MockProduct:
            def __init__(self, product_id, name, aliases=None):
                self.product_id = product_id
                self.name = name
                self.aliases = aliases or []
        
        products = [
            MockProduct("P001", "Apple MacBook Air M2", ["mac air", "macbook air"]),
            MockProduct("P002", "白萝卜", ["白蘿卜", "萝卜"]),
        ]
        
        # 测试匹配
        result = fuzzy_service.find_best_match("mac air", products)
        assert result["product_id"] == "P001"
        
        result = fuzzy_service.find_best_match("白蘿卜", products)
        assert result["product_id"] == "P002"
        
        print("✅ 模糊匹配服务测试通过")
    except Exception as e:
        print(f"❌ 模糊匹配服务测试失败: {e}")


async def test_llm_extraction():
    """测试 LLM 文本提取"""
    try:
        llm_service = LLMService()
        
        # 测试文本
        test_text = """
        客户：张三
        日期：2024-01-15
        订单：
        苹果笔记本 2台
        显示器 1台
        """
        
        result = await llm_service.extract_order_info(test_text)
        
        assert "items" in result
        assert len(result["items"]) > 0
        
        print("✅ LLM 文本提取测试通过")
        print(f"提取结果: {result}")
        
    except Exception as e:
        print(f"❌ LLM 文本提取测试失败: {e}")


if __name__ == "__main__":
    print("🧪 开始运行测试...")
    
    # 运行同步测试
    test_ocr_service_init()
    test_llm_service_init()
    test_fuzzy_matching_service()
    
    # 运行异步测试
    asyncio.run(test_llm_extraction())
    
    print("🎉 测试完成！")
