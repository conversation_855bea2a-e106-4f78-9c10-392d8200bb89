"""
配置管理
"""
from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """应用配置"""
    
    # 数据库配置
    database_url: str = "postgresql://postgres:postgres123@localhost:5432/invoice_agent"
    
    # API Keys
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    
    # 代理配置
    http_proxy: Optional[str] = "http://localhost:1080"
    https_proxy: Optional[str] = "http://localhost:1080"
    
    # 应用配置
    debug: bool = True
    log_level: str = "INFO"
    
    # 文件上传配置
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    allowed_extensions: list = [".pdf", ".jpg", ".jpeg", ".png", ".bmp", ".tiff"]
    
    # OCR 配置
    tesseract_cmd: Optional[str] = None
    ocr_languages: list = ["chi_sim", "chi_tra", "eng"]
    
    # LLM 配置
    default_llm_provider: str = "openai"  # openai 或 anthropic
    openai_model: str = "gpt-3.5-turbo"
    anthropic_model: str = "claude-3-sonnet-20240229"
    
    # 模糊匹配配置
    fuzzy_threshold: float = 0.6
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 创建全局设置实例
settings = Settings()

# 设置代理
if settings.http_proxy:
    os.environ["HTTP_PROXY"] = settings.http_proxy
if settings.https_proxy:
    os.environ["HTTPS_PROXY"] = settings.https_proxy
