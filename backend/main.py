"""
Invoice Agent Backend - FastAPI 主应用
"""
from fastapi import FastAPI, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import shutil
from pathlib import Path
from typing import List, Optional
import asyncio
from datetime import datetime

from config import settings
from database import get_db, engine, Base
from models import Order, OrderItem, Product
from services.ocr_service import OCRService
from services.llm_service import LLMService
from services.fuzzy_matching_service import FuzzyMatchingService
from services.file_service import FileService
from schemas import OrderResponse, ProcessFileRequest, ProductResponse

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Invoice Agent API",
    description="智能发票处理系统",
    version="1.0.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
ocr_service = OCRService()
llm_service = LLMService()
fuzzy_service = FuzzyMatchingService()
file_service = FileService()

# 确保上传目录存在
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)


@app.get("/")
async def root():
    """健康检查端点"""
    return {"message": "Invoice Agent API is running", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """详细健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "database": "connected",
            "ocr": "ready",
            "llm": "ready"
        }
    }


@app.post("/upload", response_model=OrderResponse)
async def upload_file(
    file: UploadFile = File(...),
    db = Depends(get_db)
):
    """
    上传文件并处理订单
    支持 PDF、JPG、PNG 等格式
    """
    try:
        # 验证文件类型
        if not file_service.is_valid_file_type(file.filename):
            raise HTTPException(
                status_code=400, 
                detail="不支持的文件类型。请上传 PDF、JPG、PNG 或其他图片文件。"
            )
        
        # 保存上传的文件
        file_path = await file_service.save_upload_file(file, UPLOAD_DIR)
        
        # 处理文件
        result = await process_file_internal(file_path, db)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")


@app.post("/process-file", response_model=OrderResponse)
async def process_file(
    request: ProcessFileRequest,
    db = Depends(get_db)
):
    """
    处理指定路径的文件（用于 n8n 工作流）
    """
    try:
        file_path = Path(request.file_path)
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        result = await process_file_internal(file_path, db)
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")


async def process_file_internal(file_path: Path, db) -> OrderResponse:
    """
    内部文件处理逻辑
    """
    # 1. OCR 文字识别
    extracted_text = await ocr_service.extract_text(file_path)
    
    # 2. LLM 结构化提取
    structured_data = await llm_service.extract_order_info(extracted_text)
    
    # 3. 获取产品列表
    products = fuzzy_service.get_all_products(db)
    
    # 4. 模糊匹配产品
    matched_items = []
    for item in structured_data.get("items", []):
        match_result = fuzzy_service.find_best_match(
            item["original_input"], 
            products
        )
        matched_items.append({
            **item,
            **match_result
        })
    
    # 5. 保存到数据库
    order = Order(
        customer_name=structured_data.get("customer_name"),
        order_date=structured_data.get("order_date"),
        original_file_path=str(file_path),
        processed_data=structured_data,
        status="completed" if all(item["match_score"] > 0.7 for item in matched_items) else "pending"
    )
    
    db.add(order)
    db.commit()
    db.refresh(order)
    
    # 保存订单项
    total_amount = 0
    for item_data in matched_items:
        if item_data["product_id"]:
            product = db.query(Product).filter(Product.product_id == item_data["product_id"]).first()
            unit_price = product.price if product else 0
            total_price = unit_price * item_data["quantity"]
            total_amount += total_price
            
            order_item = OrderItem(
                order_id=order.id,
                product_id=item_data["product_id"],
                matched_name=item_data["matched_name"],
                original_input=item_data["original_input"],
                quantity=item_data["quantity"],
                unit_price=unit_price,
                total_price=total_price,
                match_score=item_data["match_score"]
            )
            db.add(order_item)
    
    # 更新订单总金额
    order.total_amount = total_amount
    db.commit()
    
    # 构建响应
    return OrderResponse(
        id=order.id,
        customer_name=order.customer_name,
        order_date=order.order_date,
        items=matched_items,
        status=order.status,
        total_amount=total_amount
    )


@app.get("/products", response_model=List[ProductResponse])
async def get_products(db = Depends(get_db)):
    """获取所有产品列表"""
    products = db.query(Product).all()
    return products


@app.get("/orders/{order_id}", response_model=OrderResponse)
async def get_order(order_id: int, db = Depends(get_db)):
    """获取指定订单详情"""
    order = db.query(Order).filter(Order.id == order_id).first()
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    order_items = db.query(OrderItem).filter(OrderItem.order_id == order_id).all()
    
    items = []
    for item in order_items:
        items.append({
            "product_id": item.product_id,
            "matched_name": item.matched_name,
            "original_input": item.original_input,
            "quantity": item.quantity,
            "match_score": float(item.match_score) if item.match_score else 0.0
        })
    
    return OrderResponse(
        id=order.id,
        customer_name=order.customer_name,
        order_date=order.order_date,
        items=items,
        status=order.status,
        total_amount=float(order.total_amount) if order.total_amount else 0.0
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
