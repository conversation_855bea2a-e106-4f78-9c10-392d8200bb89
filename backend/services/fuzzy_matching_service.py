"""
模糊匹配服务
"""
from typing import List, Dict, Any, Optional
from rapidfuzz import fuzz, process
from sqlalchemy.orm import Session
from loguru import logger

from models import Product
from config import settings


class FuzzyMatchingService:
    """模糊匹配服务类"""
    
    def __init__(self):
        """初始化模糊匹配服务"""
        self.threshold = settings.fuzzy_threshold
        logger.info(f"模糊匹配服务初始化，阈值: {self.threshold}")
    
    def get_all_products(self, db: Session) -> List[Product]:
        """获取所有产品"""
        return db.query(Product).all()
    
    def find_best_match(self, query: str, products: List[Product]) -> Dict[str, Any]:
        """
        为查询字符串找到最佳匹配的产品
        
        Args:
            query: 查询字符串（客户输入的产品名称）
            products: 产品列表
            
        Returns:
            匹配结果字典
        """
        if not query.strip():
            return self._get_no_match_result()
        
        query = query.strip().lower()
        
        # 构建搜索候选列表
        candidates = []
        product_map = {}
        
        for product in products:
            # 主要产品名称
            candidates.extend([
                (product.name.lower(), product),
                (product.name_en.lower() if product.name_en else "", product),
                (product.name_cn.lower() if product.name_cn else "", product)
            ])
            
            # 产品别名
            if product.aliases:
                for alias in product.aliases:
                    if alias:
                        candidates.append((alias.lower(), product))
        
        # 过滤空字符串
        candidates = [(name, product) for name, product in candidates if name]
        
        if not candidates:
            return self._get_no_match_result()
        
        # 使用多种匹配算法
        best_match = None
        best_score = 0
        
        # 1. 精确匹配
        for name, product in candidates:
            if query == name:
                return self._create_match_result(product, 1.0, "exact")
        
        # 2. 包含匹配
        for name, product in candidates:
            if query in name or name in query:
                score = min(len(query), len(name)) / max(len(query), len(name))
                if score > best_score:
                    best_score = score
                    best_match = product
        
        # 3. 模糊匹配
        names_only = [name for name, _ in candidates]
        fuzzy_results = process.extract(
            query, 
            names_only, 
            scorer=fuzz.WRatio,
            limit=5
        )
        
        for matched_name, score, _ in fuzzy_results:
            normalized_score = score / 100.0
            if normalized_score > best_score:
                # 找到对应的产品
                for name, product in candidates:
                    if name == matched_name:
                        best_score = normalized_score
                        best_match = product
                        break
        
        # 4. 部分匹配（针对中文）
        if best_score < self.threshold:
            best_score, best_match = self._chinese_partial_match(query, candidates, best_score, best_match)
        
        # 返回结果
        if best_match and best_score >= self.threshold:
            confidence = self._get_confidence_level(best_score)
            return self._create_match_result(best_match, best_score, confidence)
        else:
            return self._get_no_match_result()
    
    def _chinese_partial_match(self, query: str, candidates: List[tuple], current_best_score: float, current_best_match: Optional[Product]) -> tuple:
        """中文部分匹配"""
        best_score = current_best_score
        best_match = current_best_match
        
        # 检查是否包含中文字符
        if not any('\u4e00' <= char <= '\u9fff' for char in query):
            return best_score, best_match
        
        for name, product in candidates:
            # 计算共同字符数
            common_chars = set(query) & set(name)
            if common_chars:
                # 计算字符重叠度
                overlap_score = len(common_chars) / max(len(set(query)), len(set(name)))
                
                # 如果查询是名称的子集或反之
                if set(query).issubset(set(name)) or set(name).issubset(set(query)):
                    overlap_score *= 1.2  # 提升子集匹配的分数
                
                if overlap_score > best_score:
                    best_score = overlap_score
                    best_match = product
        
        return best_score, best_match
    
    def _create_match_result(self, product: Product, score: float, confidence: str) -> Dict[str, Any]:
        """创建匹配结果"""
        return {
            "product_id": product.product_id,
            "matched_name": product.name,
            "match_score": round(score, 2),
            "confidence": confidence
        }
    
    def _get_no_match_result(self) -> Dict[str, Any]:
        """获取无匹配结果"""
        return {
            "product_id": None,
            "matched_name": None,
            "match_score": 0.0,
            "confidence": "none"
        }
    
    def _get_confidence_level(self, score: float) -> str:
        """根据分数获取置信度级别"""
        if score >= 0.9:
            return "high"
        elif score >= 0.7:
            return "medium"
        elif score >= self.threshold:
            return "low"
        else:
            return "none"
    
    def batch_match(self, queries: List[str], products: List[Product]) -> List[Dict[str, Any]]:
        """批量匹配"""
        results = []
        for query in queries:
            result = self.find_best_match(query, products)
            results.append(result)
        return results
    
    def get_match_suggestions(self, query: str, products: List[Product], limit: int = 5) -> List[Dict[str, Any]]:
        """获取匹配建议列表"""
        if not query.strip():
            return []
        
        query = query.strip().lower()
        suggestions = []
        
        # 构建候选列表
        candidates = []
        for product in products:
            candidates.extend([
                (product.name.lower(), product),
                (product.name_en.lower() if product.name_en else "", product),
                (product.name_cn.lower() if product.name_cn else "", product)
            ])
            
            if product.aliases:
                for alias in product.aliases:
                    if alias:
                        candidates.append((alias.lower(), product))
        
        candidates = [(name, product) for name, product in candidates if name]
        
        # 模糊匹配获取多个建议
        names_only = [name for name, _ in candidates]
        fuzzy_results = process.extract(
            query, 
            names_only, 
            scorer=fuzz.WRatio,
            limit=limit * 2  # 获取更多结果以便去重
        )
        
        seen_products = set()
        for matched_name, score, _ in fuzzy_results:
            normalized_score = score / 100.0
            
            # 找到对应的产品
            for name, product in candidates:
                if name == matched_name and product.product_id not in seen_products:
                    confidence = self._get_confidence_level(normalized_score)
                    suggestions.append({
                        "product_id": product.product_id,
                        "matched_name": product.name,
                        "match_score": round(normalized_score, 2),
                        "confidence": confidence
                    })
                    seen_products.add(product.product_id)
                    break
            
            if len(suggestions) >= limit:
                break
        
        return suggestions
