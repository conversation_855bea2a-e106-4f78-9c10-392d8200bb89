"""
OCR 文字识别服务
"""
import easyocr
import cv2
import numpy as np
from PIL import Image
from pdf2image import convert_from_path
from pathlib import Path
from typing import List, Union
import asyncio
from loguru import logger

from config import settings


class OCRService:
    """OCR 文字识别服务类"""
    
    def __init__(self):
        """初始化 OCR 服务"""
        self.reader = None
        self._initialize_reader()
    
    def _initialize_reader(self):
        """初始化 EasyOCR 读取器"""
        try:
            self.reader = easyocr.Reader(
                settings.ocr_languages,
                gpu=False  # 在 Docker 中通常使用 CPU
            )
            logger.info(f"OCR 服务初始化成功，支持语言: {settings.ocr_languages}")
        except Exception as e:
            logger.error(f"OCR 服务初始化失败: {e}")
            raise
    
    async def extract_text(self, file_path: Union[str, Path]) -> str:
        """
        从文件中提取文字
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的文字内容
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            if file_path.suffix.lower() == '.pdf':
                return await self._extract_from_pdf(file_path)
            else:
                return await self._extract_from_image(file_path)
        except Exception as e:
            logger.error(f"文字提取失败 {file_path}: {e}")
            raise
    
    async def _extract_from_pdf(self, pdf_path: Path) -> str:
        """从 PDF 文件提取文字"""
        logger.info(f"开始处理 PDF 文件: {pdf_path}")
        
        # 将 PDF 转换为图片
        images = convert_from_path(str(pdf_path))
        
        all_text = []
        for i, image in enumerate(images):
            logger.info(f"处理 PDF 第 {i+1} 页")
            
            # 将 PIL 图片转换为 numpy 数组
            image_array = np.array(image)
            
            # 使用 EasyOCR 识别文字
            text = await self._ocr_image_array(image_array)
            if text.strip():
                all_text.append(f"=== 第 {i+1} 页 ===\n{text}")
        
        result = "\n\n".join(all_text)
        logger.info(f"PDF 文字提取完成，共 {len(images)} 页，提取文字长度: {len(result)}")
        return result
    
    async def _extract_from_image(self, image_path: Path) -> str:
        """从图片文件提取文字"""
        logger.info(f"开始处理图片文件: {image_path}")
        
        # 读取图片
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"无法读取图片文件: {image_path}")
        
        # 图片预处理
        processed_image = self._preprocess_image(image)
        
        # OCR 识别
        text = await self._ocr_image_array(processed_image)
        
        logger.info(f"图片文字提取完成，提取文字长度: {len(text)}")
        return text
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图片预处理，提高 OCR 准确率"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
        
        # 去噪
        denoised = cv2.medianBlur(gray, 3)
        
        # 自适应阈值处理
        thresh = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        return thresh
    
    async def _ocr_image_array(self, image_array: np.ndarray) -> str:
        """对图片数组进行 OCR 识别"""
        def _run_ocr():
            try:
                results = self.reader.readtext(image_array)
                
                # 提取文字并按位置排序
                text_blocks = []
                for (bbox, text, confidence) in results:
                    if confidence > 0.3:  # 过滤低置信度的结果
                        # 获取文本块的 y 坐标用于排序
                        y_coord = bbox[0][1]
                        text_blocks.append((y_coord, text))
                
                # 按 y 坐标排序，模拟从上到下的阅读顺序
                text_blocks.sort(key=lambda x: x[0])
                
                # 合并文字
                extracted_text = "\n".join([text for _, text in text_blocks])
                return extracted_text
                
            except Exception as e:
                logger.error(f"OCR 识别过程出错: {e}")
                return ""
        
        # 在线程池中运行 OCR（因为 EasyOCR 是同步的）
        loop = asyncio.get_event_loop()
        text = await loop.run_in_executor(None, _run_ocr)
        
        return text
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        return ['.pdf', '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
