"""
文件处理服务
"""
import os
import shutil
import uuid
from pathlib import Path
from typing import Optional
import aiofiles
from fastapi import UploadFile
from loguru import logger

from config import settings


class FileService:
    """文件处理服务类"""
    
    def __init__(self):
        """初始化文件服务"""
        self.allowed_extensions = set(settings.allowed_extensions)
        self.max_file_size = settings.max_file_size
        logger.info(f"文件服务初始化，支持格式: {self.allowed_extensions}")
    
    def is_valid_file_type(self, filename: Optional[str]) -> bool:
        """检查文件类型是否有效"""
        if not filename:
            return False
        
        file_ext = Path(filename).suffix.lower()
        return file_ext in self.allowed_extensions
    
    def is_valid_file_size(self, file_size: int) -> bool:
        """检查文件大小是否有效"""
        return file_size <= self.max_file_size
    
    async def save_upload_file(self, upload_file: UploadFile, upload_dir: Path) -> Path:
        """
        保存上传的文件
        
        Args:
            upload_file: FastAPI 上传文件对象
            upload_dir: 上传目录
            
        Returns:
            保存的文件路径
        """
        # 验证文件类型
        if not self.is_valid_file_type(upload_file.filename):
            raise ValueError(f"不支持的文件类型: {upload_file.filename}")
        
        # 生成唯一文件名
        file_ext = Path(upload_file.filename).suffix.lower()
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        file_path = upload_dir / unique_filename
        
        # 确保上传目录存在
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            # 异步保存文件
            async with aiofiles.open(file_path, 'wb') as f:
                content = await upload_file.read()
                
                # 检查文件大小
                if not self.is_valid_file_size(len(content)):
                    raise ValueError(f"文件大小超过限制: {len(content)} bytes")
                
                await f.write(content)
            
            logger.info(f"文件保存成功: {file_path}")
            return file_path
            
        except Exception as e:
            # 如果保存失败，清理已创建的文件
            if file_path.exists():
                file_path.unlink()
            logger.error(f"文件保存失败: {e}")
            raise
    
    def delete_file(self, file_path: Path) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否删除成功
        """
        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"文件删除成功: {file_path}")
                return True
            else:
                logger.warning(f"文件不存在: {file_path}")
                return False
        except Exception as e:
            logger.error(f"文件删除失败: {e}")
            return False
    
    def get_file_info(self, file_path: Path) -> dict:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        stat = file_path.stat()
        
        return {
            "filename": file_path.name,
            "size": stat.st_size,
            "extension": file_path.suffix.lower(),
            "created_time": stat.st_ctime,
            "modified_time": stat.st_mtime,
            "is_valid_type": self.is_valid_file_type(file_path.name),
            "is_valid_size": self.is_valid_file_size(stat.st_size)
        }
    
    def cleanup_old_files(self, directory: Path, max_age_days: int = 7) -> int:
        """
        清理旧文件
        
        Args:
            directory: 目录路径
            max_age_days: 最大保留天数
            
        Returns:
            删除的文件数量
        """
        if not directory.exists():
            return 0
        
        import time
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60
        deleted_count = 0
        
        try:
            for file_path in directory.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        if self.delete_file(file_path):
                            deleted_count += 1
            
            logger.info(f"清理完成，删除了 {deleted_count} 个旧文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
            return deleted_count
