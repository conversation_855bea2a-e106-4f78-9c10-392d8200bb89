"""
LLM 服务 - 用于结构化数据提取
"""
import json
import re
from typing import Dict, Any, Optional
import asyncio
from loguru import logger

try:
    import openai
except ImportError:
    openai = None

try:
    import anthropic
except ImportError:
    anthropic = None

from config import settings


class LLMService:
    """LLM 服务类"""
    
    def __init__(self):
        """初始化 LLM 服务"""
        self.openai_client = None
        self.anthropic_client = None
        
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化 LLM 客户端"""
        # 初始化 OpenAI 客户端
        if settings.openai_api_key and openai:
            try:
                self.openai_client = openai.OpenAI(
                    api_key=settings.openai_api_key,
                    http_client=self._get_http_client()
                )
                logger.info("OpenAI 客户端初始化成功")
            except Exception as e:
                logger.warning(f"OpenAI 客户端初始化失败: {e}")
        
        # 初始化 Anthropic 客户端
        if settings.anthropic_api_key and anthropic:
            try:
                self.anthropic_client = anthropic.Anthropic(
                    api_key=settings.anthropic_api_key
                )
                logger.info("Anthropic 客户端初始化成功")
            except Exception as e:
                logger.warning(f"Anthropic 客户端初始化失败: {e}")
    
    def _get_http_client(self):
        """获取配置了代理的 HTTP 客户端"""
        import httpx
        
        proxies = {}
        if settings.http_proxy:
            proxies["http://"] = settings.http_proxy
        if settings.https_proxy:
            proxies["https://"] = settings.https_proxy
        
        if proxies:
            return httpx.Client(proxies=proxies)
        return None
    
    async def extract_order_info(self, text: str) -> Dict[str, Any]:
        """
        从文本中提取订单信息
        
        Args:
            text: OCR 提取的文本
            
        Returns:
            结构化的订单信息
        """
        if not text.strip():
            return self._get_empty_order()
        
        try:
            # 根据配置选择 LLM 提供商
            if settings.default_llm_provider == "openai" and self.openai_client:
                return await self._extract_with_openai(text)
            elif settings.default_llm_provider == "anthropic" and self.anthropic_client:
                return await self._extract_with_anthropic(text)
            else:
                # 回退到规则提取
                logger.warning("没有可用的 LLM 服务，使用规则提取")
                return self._extract_with_rules(text)
                
        except Exception as e:
            logger.error(f"LLM 提取失败: {e}")
            # 回退到规则提取
            return self._extract_with_rules(text)
    
    async def _extract_with_openai(self, text: str) -> Dict[str, Any]:
        """使用 OpenAI 提取订单信息"""
        prompt = self._get_extraction_prompt(text)
        
        def _call_openai():
            response = self.openai_client.chat.completions.create(
                model=settings.openai_model,
                messages=[
                    {"role": "system", "content": "你是一个专业的订单信息提取助手。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1000
            )
            return response.choices[0].message.content
        
        # 在线程池中运行
        loop = asyncio.get_event_loop()
        response_text = await loop.run_in_executor(None, _call_openai)
        
        return self._parse_llm_response(response_text)
    
    async def _extract_with_anthropic(self, text: str) -> Dict[str, Any]:
        """使用 Anthropic 提取订单信息"""
        prompt = self._get_extraction_prompt(text)
        
        def _call_anthropic():
            response = self.anthropic_client.messages.create(
                model=settings.anthropic_model,
                max_tokens=1000,
                temperature=0.1,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            return response.content[0].text
        
        # 在线程池中运行
        loop = asyncio.get_event_loop()
        response_text = await loop.run_in_executor(None, _call_anthropic)
        
        return self._parse_llm_response(response_text)
    
    def _get_extraction_prompt(self, text: str) -> str:
        """构建提取提示词"""
        return f"""
请从以下文本中提取订单信息，并以 JSON 格式返回。

文本内容：
{text}

请提取以下信息：
1. customer_name: 客户姓名
2. order_date: 订单日期 (YYYY-MM-DD 格式)
3. items: 订单项目列表，每个项目包含：
   - original_input: 原始产品名称/描述
   - quantity: 数量

返回格式示例：
{{
    "customer_name": "张三",
    "order_date": "2024-01-15",
    "items": [
        {{
            "original_input": "苹果笔记本",
            "quantity": 2
        }},
        {{
            "original_input": "显示器",
            "quantity": 1
        }}
    ]
}}

注意：
- 如果某些信息无法确定，请设为 null
- 数量必须是整数
- 只返回 JSON，不要其他说明文字
"""
    
    def _parse_llm_response(self, response_text: str) -> Dict[str, Any]:
        """解析 LLM 响应"""
        try:
            # 尝试提取 JSON
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
                
                # 验证和清理数据
                return self._validate_extracted_data(data)
            else:
                logger.warning("LLM 响应中未找到有效的 JSON")
                return self._get_empty_order()
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析失败: {e}")
            return self._get_empty_order()
    
    def _extract_with_rules(self, text: str) -> Dict[str, Any]:
        """使用规则提取订单信息（回退方案）"""
        logger.info("使用规则提取订单信息")
        
        # 简单的规则提取
        lines = text.split('\n')
        items = []
        customer_name = None
        order_date = None
        
        # 查找客户姓名
        for line in lines:
            if any(keyword in line for keyword in ['客户', '姓名', '收货人']):
                # 简单提取姓名
                name_match = re.search(r'[：:]\s*([^\s]+)', line)
                if name_match:
                    customer_name = name_match.group(1)
                break
        
        # 查找日期
        date_pattern = r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})'
        for line in lines:
            date_match = re.search(date_pattern, line)
            if date_match:
                order_date = date_match.group(1).replace('/', '-')
                break
        
        # 查找商品项目
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 查找数量模式
            quantity_patterns = [
                r'(\d+)\s*[个件只斤包袋盒]',
                r'[×x]\s*(\d+)',
                r'(\d+)\s*$'
            ]
            
            quantity = 1
            for pattern in quantity_patterns:
                match = re.search(pattern, line)
                if match:
                    quantity = int(match.group(1))
                    break
            
            # 如果行中包含可能的商品名称
            if len(line) > 1 and not line.isdigit():
                # 清理数量信息，保留商品名称
                clean_name = re.sub(r'\d+\s*[个件只斤包袋盒]', '', line)
                clean_name = re.sub(r'[×x]\s*\d+', '', clean_name)
                clean_name = clean_name.strip()
                
                if clean_name:
                    items.append({
                        "original_input": clean_name,
                        "quantity": quantity
                    })
        
        return {
            "customer_name": customer_name,
            "order_date": order_date,
            "items": items
        }
    
    def _validate_extracted_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理提取的数据"""
        validated = {
            "customer_name": data.get("customer_name"),
            "order_date": data.get("order_date"),
            "items": []
        }
        
        # 验证订单项
        items = data.get("items", [])
        for item in items:
            if isinstance(item, dict) and "original_input" in item:
                validated_item = {
                    "original_input": str(item["original_input"]).strip(),
                    "quantity": max(1, int(item.get("quantity", 1)))
                }
                if validated_item["original_input"]:
                    validated["items"].append(validated_item)
        
        return validated
    
    def _get_empty_order(self) -> Dict[str, Any]:
        """获取空订单结构"""
        return {
            "customer_name": None,
            "order_date": None,
            "items": []
        }
