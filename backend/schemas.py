"""
Pydantic 模式定义
"""
from pydantic import BaseModel
from typing import List, Optional
from datetime import date
from decimal import Decimal


class ProductBase(BaseModel):
    """产品基础模式"""
    product_id: str
    name: str
    name_en: Optional[str] = None
    name_cn: Optional[str] = None
    price: Decimal
    category: Optional[str] = None
    description: Optional[str] = None
    aliases: Optional[List[str]] = []


class ProductResponse(ProductBase):
    """产品响应模式"""
    id: int
    
    class Config:
        from_attributes = True


class OrderItemBase(BaseModel):
    """订单项基础模式"""
    product_id: Optional[str] = None
    matched_name: Optional[str] = None
    original_input: str
    quantity: int
    match_score: Optional[float] = None


class OrderItemResponse(OrderItemBase):
    """订单项响应模式"""
    id: Optional[int] = None
    unit_price: Optional[Decimal] = None
    total_price: Optional[Decimal] = None
    
    class Config:
        from_attributes = True


class OrderBase(BaseModel):
    """订单基础模式"""
    customer_name: Optional[str] = None
    order_date: Optional[date] = None
    status: str = "pending"


class OrderResponse(OrderBase):
    """订单响应模式"""
    id: int
    items: List[dict]
    total_amount: Optional[float] = None
    
    class Config:
        from_attributes = True


class ProcessFileRequest(BaseModel):
    """处理文件请求模式"""
    file_path: str


class MatchResult(BaseModel):
    """匹配结果模式"""
    product_id: Optional[str] = None
    matched_name: Optional[str] = None
    match_score: float = 0.0
    confidence: str = "low"  # low, medium, high


class ExtractedOrderInfo(BaseModel):
    """提取的订单信息模式"""
    customer_name: Optional[str] = None
    order_date: Optional[str] = None
    items: List[dict] = []
    raw_text: Optional[str] = None
