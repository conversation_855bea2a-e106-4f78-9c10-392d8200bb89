# Invoice Agent - 智能发票处理系统

基于 n8n 工作流的智能发票处理系统，能够自动识别订单文件中的产品信息并进行智能匹配。

## 🚀 功能特性

- **多格式支持**: 支持 PDF、JPG、PNG 等多种文件格式
- **智能识别**: 使用 OCR 技术提取文字信息
- **LLM 增强**: 集成 OpenAI/Claude 进行结构化数据提取
- **模糊匹配**: 智能产品名称匹配，处理拼写错误和别名
- **工作流自动化**: 基于 n8n 的可视化工作流
- **实时处理**: 支持文件上传后立即处理
- **人工审核**: 低置信度匹配自动触发人工确认流程

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端上传界面   │───▶│   n8n 工作流    │───▶│  FastAPI 后端   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   通知系统      │    │ PostgreSQL 数据库│
                       │  (Slack/Email)  │    │                 │
                       └─────────────────┘    └─────────────────┘
```

## 📋 技术栈

- **后端**: FastAPI + Python
- **数据库**: PostgreSQL
- **工作流**: n8n
- **前端**: HTML/CSS/JavaScript
- **OCR**: EasyOCR
- **LLM**: OpenAI GPT / Anthropic Claude
- **模糊匹配**: RapidFuzz
- **容器化**: Docker + Docker Compose
- **包管理**: uv (Python), pnpm (Node.js)

## 🛠️ 快速开始

### 1. 环境准备

确保已安装以下软件：
- Docker & Docker Compose
- Git

### 2. 克隆项目

```bash
git clone <repository-url>
cd n8n-demo-002
```

### 3. 配置环境变量

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入必要的 API 密钥：

```env
# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 代理配置（如需要）
HTTP_PROXY=http://localhost:1080
HTTPS_PROXY=http://localhost:1080
```

### 4. 启动服务

```bash
docker-compose up -d
```

### 5. 访问服务

- **前端上传界面**: http://localhost:3000
- **n8n 工作流管理**: http://localhost:5678 (admin/admin123)
- **后端 API 文档**: http://localhost:8000/docs
- **数据库**: localhost:5432 (postgres/postgres123)

## 📖 使用说明

### 文件上传处理

1. 访问 http://localhost:3000
2. 上传订单文件（PDF、图片等）
3. 系统自动进行 OCR 识别和产品匹配
4. 查看处理结果和匹配详情

### n8n 工作流

1. 访问 http://localhost:5678
2. 使用 admin/admin123 登录
3. 导入 `n8n/workflows/invoice-processing-workflow.json`
4. 配置 Webhook 和通知设置
5. 激活工作流

### API 接口

主要 API 端点：

- `POST /upload` - 上传文件处理
- `POST /process-file` - 处理指定文件
- `GET /orders/{id}` - 获取订单详情
- `GET /products` - 获取产品列表

详细 API 文档：http://localhost:8000/docs

## 🔧 配置说明

### 产品数据

系统预置了示例产品数据，包括：
- 电子产品（MacBook、iPhone、显示器等）
- 食品蔬菜（白萝卜、皮蛋、芹菜等）

可通过数据库直接添加更多产品和别名。

### 模糊匹配配置

在 `backend/config.py` 中调整匹配参数：

```python
# 模糊匹配阈值
fuzzy_threshold: float = 0.6

# OCR 语言支持
ocr_languages: list = ["chi_sim", "chi_tra", "eng"]
```

### LLM 配置

支持 OpenAI 和 Anthropic 两种 LLM 提供商：

```python
# 默认 LLM 提供商
default_llm_provider: str = "openai"  # openai 或 anthropic
openai_model: str = "gpt-3.5-turbo"
anthropic_model: str = "claude-3-sonnet-20240229"
```

## 🧪 测试

### 运行后端测试

```bash
cd backend
uv run pytest
```

### 测试文件上传

使用提供的示例文件测试：
- 中文订单图片
- PDF 订单文件
- 手写订单扫描件

## 📊 监控和日志

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f n8n
```

### 数据库监控

连接 PostgreSQL 查看处理记录：

```bash
docker-compose exec postgres psql -U postgres -d invoice_agent
```

## 🔍 故障排除

### 常见问题

1. **OCR 识别效果不佳**
   - 检查图片质量和分辨率
   - 调整 OCR 预处理参数
   - 尝试不同的 OCR 语言设置

2. **LLM API 调用失败**
   - 检查 API 密钥配置
   - 确认代理设置（如使用代理）
   - 查看网络连接状态

3. **模糊匹配准确率低**
   - 增加产品别名数据
   - 调整匹配阈值
   - 优化产品名称标准化

### 重置系统

```bash
# 停止所有服务
docker-compose down

# 清理数据卷（注意：会删除所有数据）
docker-compose down -v

# 重新启动
docker-compose up -d
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请通过以下方式联系：
- 创建 GitHub Issue
- 发送邮件至 <EMAIL>
