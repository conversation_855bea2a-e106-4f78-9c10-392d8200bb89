# 🚀 Invoice Agent 快速启动指南

## 立即体验（无需安装任何依赖）

```bash
# 运行核心功能演示
python3 simple_demo.py
```

这将展示完整的订单处理流程，包括文本提取、产品匹配和价格计算。

## 完整系统部署

### 方式一：Docker 一键部署（推荐）

```bash
# 1. 配置环境变量
cp .env.example .env

# 2. 编辑 .env 文件，填入 API 密钥（可选）
# OPENAI_API_KEY=your_key_here
# ANTHROPIC_API_KEY=your_key_here

# 3. 一键启动
./start.sh

# 或者手动启动
docker-compose up -d --build
```

### 方式二：本地开发环境

```bash
# 后端开发
cd backend
uv sync                    # 安装依赖
uv run uvicorn main:app --reload  # 启动后端

# 前端开发
cd frontend
pnpm install              # 安装依赖
pnpm start               # 启动前端
```

## 访问地址

启动成功后，访问以下地址：

- **前端上传界面**: http://localhost:3000
- **n8n 工作流管理**: http://localhost:5678 (admin/admin123)
- **后端 API 文档**: http://localhost:8000/docs
- **数据库**: localhost:5432 (postgres/postgres123)

## 使用流程

1. **上传文件**: 访问 http://localhost:3000，上传订单文件
2. **查看结果**: 系统自动处理并显示匹配结果
3. **配置工作流**: 在 n8n 中自定义处理流程
4. **API 集成**: 使用 REST API 集成到现有系统

## 测试数据

项目包含示例测试数据：
- `test-data/sample-order.txt` - 示例订单文本
- 预置产品数据（电子产品、食品蔬菜等）

## 故障排除

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 完全重置
./stop.sh
```

## 核心功能验证

运行 `python3 simple_demo.py` 应该看到类似输出：

```
🚀 Invoice Agent 简单演示
✅ 苹果笔记本电脑 MacBook Air M2 -> Apple MacBook Air M2 (分数: 0.68)
✅ 戴尔24寸显示器 -> Dell 24寸显示器 (分数: 0.89)
💰 订单总金额: ¥19384.0
🎉 演示完成！
```

## 下一步

1. 配置真实的 API 密钥以启用 LLM 功能
2. 上传真实的订单文件进行测试
3. 在 n8n 中配置自定义工作流
4. 根据需要添加更多产品数据

**🎯 项目已就绪，开始体验智能发票处理吧！**
