{"name": "Invoice Processing Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "invoice-webhook", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "invoice-webhook"}, {"parameters": {"url": "http://backend:8000/process-file", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "file_path", "value": "={{ $json.file_path }}"}]}, "options": {"timeout": 60000}}, "id": "process-file", "name": "Process File", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.status }}", "rightValue": "completed", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "check-status", "name": "Check Status", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"url": "http://backend:8000/orders/{{ $json.id }}", "options": {}}, "id": "get-order-details", "name": "Get Order Details", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}"}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "⚠️ 订单处理需要人工确认\\n订单ID: {{ $json.id }}\\n客户: {{ $json.customer_name }}\\n状态: {{ $json.status }}\\n原因: 部分产品匹配度较低"}, {"name": "channel", "value": "#invoice-alerts"}]}, "options": {}}, "id": "send-alert", "name": "<PERSON> <PERSON><PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"status\": \"pending\", \"message\": \"订单已创建，等待人工确认\", \"order_id\": $json.id } }}"}, "id": "pending-response", "name": "Pending Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO order_logs (order_id, action, details, created_at) VALUES ({{ $json.id }}, 'processed', '{{ JSON.stringify($json) }}', NOW())", "options": {}}, "id": "log-to-database", "name": "Log to Database", "type": "n8n-nodes-base.postgres", "typeVersion": 2.4, "position": [680, 500], "credentials": {"postgres": {"id": "postgres-credentials", "name": "PostgreSQL Credentials"}}}], "connections": {"Webhook Trigger": {"main": [[{"node": "Process File", "type": "main", "index": 0}]]}, "Process File": {"main": [[{"node": "Check Status", "type": "main", "index": 0}, {"node": "Log to Database", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "Get Order Details", "type": "main", "index": 0}], [{"node": "<PERSON> <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Get Order Details": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Send Alert": {"main": [[{"node": "Pending Response", "type": "main", "index": 0}]]}}, "createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "id": "invoice-processing", "name": "Invoice Processing"}], "triggerCount": 1, "versionId": "1"}