-- 创建 n8n 数据库
CREATE DATABASE n8n;

-- 创建产品表
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    product_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    name_cn VARCHAR(255),
    price DECIMAL(10, 2) NOT NULL,
    category VARCHAR(100),
    description TEXT,
    aliases TEXT[], -- 存储产品别名
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    customer_name VARCHAR(255),
    order_date DATE,
    status VARCHAR(50) DEFAULT 'pending',
    total_amount DECIMAL(10, 2),
    original_file_path VARCHAR(500),
    processed_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建订单项表
CREATE TABLE IF NOT EXISTS order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_id VARCHAR(50) REFERENCES products(product_id),
    matched_name VARCHAR(255),
    original_input VARCHAR(255),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10, 2),
    total_price DECIMAL(10, 2),
    match_score DECIMAL(3, 2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入示例产品数据
INSERT INTO products (product_id, name, name_en, name_cn, price, category, aliases) VALUES
('P001', 'Apple MacBook Air M2', 'Apple MacBook Air M2', '苹果 MacBook Air M2', 8999.00, 'Electronics', ARRAY['mac air', 'macbook air', 'mac air m2', '苹果笔记本']),
('P002', 'iPhone 15 Pro', 'iPhone 15 Pro', 'iPhone 15 Pro', 7999.00, 'Electronics', ARRAY['iphone 15', 'iphone15', '苹果手机', '爱疯15']),
('P003', 'Dell 24" Monitor', 'Dell 24" Monitor', '戴尔24寸显示器', 1299.00, 'Electronics', ARRAY['dell monitor', '24 dell screen', '戴尔显示器', 'dell 24']),
('P004', '白萝卜', 'White Radish', '白萝卜', 3.50, 'Vegetables', ARRAY['白蘿卜', '萝卜', '白萝卜']),
('P005', '皮蛋', 'Century Egg', '皮蛋', 2.00, 'Food', ARRAY['皮一蛋', '松花蛋', '变蛋']),
('P006', '蒜苗', 'Garlic Sprouts', '蒜苗', 8.00, 'Vegetables', ARRAY['蒜苗', '青蒜', '蒜薹']),
('P007', '芹菜', 'Celery', '芹菜', 4.50, 'Vegetables', ARRAY['芹学', '西芹', '药芹']),
('P008', '红薯', 'Sweet Potato', '红薯', 5.00, 'Vegetables', ARRAY['甜薯', '地瓜', '番薯']),
('P009', '韭菜', 'Chinese Chives', '韭菜', 6.00, 'Vegetables', ARRAY['韭菜', '起阳草']),
('P010', '大豆', 'Soybean', '大豆', 12.00, 'Grains', ARRAY['黄豆', '大豆', '毛豆']),
('P011', '沙拉', 'Salad', '沙拉', 15.00, 'Food', ARRAY['沙拉', '色拉']);

-- 创建索引
CREATE INDEX idx_products_name ON products USING gin(to_tsvector('english', name));
CREATE INDEX idx_products_aliases ON products USING gin(aliases);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表添加更新时间触发器
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
