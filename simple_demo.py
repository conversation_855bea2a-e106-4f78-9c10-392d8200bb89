#!/usr/bin/env python3
"""
Invoice Agent 简单演示
不依赖复杂的外部库，展示核心功能
"""
import re
import json
from typing import List, Dict, Any
from difflib import SequenceMatcher


class SimpleProduct:
    """简单产品类"""
    def __init__(self, product_id: str, name: str, price: float, aliases: List[str] = None):
        self.product_id = product_id
        self.name = name
        self.price = price
        self.aliases = aliases or []


class SimpleFuzzyMatcher:
    """简单模糊匹配器"""
    
    def __init__(self, threshold: float = 0.6):
        self.threshold = threshold
    
    def similarity(self, a: str, b: str) -> float:
        """计算两个字符串的相似度"""
        return SequenceMatcher(None, a.lower(), b.lower()).ratio()
    
    def find_best_match(self, query: str, products: List[SimpleProduct]) -> Dict[str, Any]:
        """找到最佳匹配的产品"""
        if not query.strip():
            return {"product_id": None, "matched_name": None, "match_score": 0.0}
        
        query = query.strip().lower()
        best_match = None
        best_score = 0.0
        
        for product in products:
            # 检查主要名称
            score = self.similarity(query, product.name)
            if score > best_score:
                best_score = score
                best_match = product
            
            # 检查别名
            for alias in product.aliases:
                score = self.similarity(query, alias)
                if score > best_score:
                    best_score = score
                    best_match = product
        
        if best_match and best_score >= self.threshold:
            return {
                "product_id": best_match.product_id,
                "matched_name": best_match.name,
                "match_score": round(best_score, 2)
            }
        else:
            return {"product_id": None, "matched_name": None, "match_score": 0.0}


class SimpleOrderExtractor:
    """简单订单信息提取器"""
    
    def extract_order_info(self, text: str) -> Dict[str, Any]:
        """从文本中提取订单信息"""
        lines = text.split('\n')
        
        customer_name = None
        order_date = None
        items = []
        
        # 查找客户姓名
        for line in lines:
            if any(keyword in line for keyword in ['客户', '姓名', '收货人']):
                name_match = re.search(r'[：:]\s*([^\s]+)', line)
                if name_match:
                    customer_name = name_match.group(1)
                break
        
        # 查找日期
        date_pattern = r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})'
        for line in lines:
            date_match = re.search(date_pattern, line)
            if date_match:
                order_date = date_match.group(1).replace('/', '-')
                break
        
        # 查找商品项目
        for line in lines:
            line = line.strip()
            if not line or line.startswith(('客户', '日期', '订单', '备注', '总计')):
                continue
            
            # 查找数量模式
            quantity_patterns = [
                r'(\d+)\s*[个件只斤包袋盒台]',
                r'[-]\s*(\d+)',
                r'(\d+)\s*$'
            ]
            
            quantity = 1
            for pattern in quantity_patterns:
                match = re.search(pattern, line)
                if match:
                    quantity = int(match.group(1))
                    break
            
            # 清理商品名称
            clean_name = re.sub(r'\d+\s*[个件只斤包袋盒台]', '', line)
            clean_name = re.sub(r'[-]\s*\d+', '', clean_name)
            clean_name = re.sub(r'^\d+\.\s*', '', clean_name)  # 移除序号
            clean_name = clean_name.strip()
            
            if clean_name and len(clean_name) > 1:
                items.append({
                    "original_input": clean_name,
                    "quantity": quantity
                })
        
        return {
            "customer_name": customer_name,
            "order_date": order_date,
            "items": items
        }


def create_sample_products() -> List[SimpleProduct]:
    """创建示例产品数据"""
    return [
        SimpleProduct("P001", "Apple MacBook Air M2", 8999.00, 
                     ["mac air", "macbook air", "苹果笔记本", "苹果笔记本电脑"]),
        SimpleProduct("P002", "iPhone 15 Pro", 7999.00, 
                     ["iphone 15", "iphone15", "苹果手机", "爱疯15"]),
        SimpleProduct("P003", "Dell 24寸显示器", 1299.00, 
                     ["dell monitor", "24 dell screen", "戴尔显示器", "dell 24", "戴尔24寸显示器"]),
        SimpleProduct("P004", "白萝卜", 3.50, 
                     ["白蘿卜", "萝卜"]),
        SimpleProduct("P005", "皮蛋", 2.00, 
                     ["皮一蛋", "松花蛋", "变蛋"]),
        SimpleProduct("P006", "蒜苗", 8.00, 
                     ["蒜苗", "青蒜", "蒜薹"]),
        SimpleProduct("P007", "芹菜", 4.50, 
                     ["芹学", "西芹", "药芹"]),
    ]


def process_order(text: str) -> Dict[str, Any]:
    """处理订单的完整流程"""
    print("🔍 开始处理订单...")
    
    # 1. 提取订单信息
    extractor = SimpleOrderExtractor()
    order_info = extractor.extract_order_info(text)
    print(f"📋 提取的订单信息: {order_info}")
    
    # 2. 获取产品列表
    products = create_sample_products()
    print(f"📦 加载了 {len(products)} 个产品")
    
    # 3. 模糊匹配
    matcher = SimpleFuzzyMatcher()
    matched_items = []
    total_amount = 0.0
    
    for item in order_info["items"]:
        match_result = matcher.find_best_match(item["original_input"], products)
        
        # 计算价格
        unit_price = 0.0
        total_price = 0.0
        if match_result["product_id"]:
            product = next(p for p in products if p.product_id == match_result["product_id"])
            unit_price = product.price
            total_price = unit_price * item["quantity"]
            total_amount += total_price
        
        matched_item = {
            **item,
            **match_result,
            "unit_price": unit_price,
            "total_price": total_price
        }
        matched_items.append(matched_item)
        
        print(f"🔗 匹配结果: {item['original_input']} -> {match_result['matched_name']} (分数: {match_result['match_score']})")
    
    # 4. 构建最终结果
    result = {
        "customer_name": order_info["customer_name"],
        "order_date": order_info["order_date"],
        "items": matched_items,
        "total_amount": round(total_amount, 2),
        "status": "completed" if all(item["match_score"] > 0.6 for item in matched_items if item["match_score"] > 0) else "pending"
    }
    
    return result


def main():
    """主函数"""
    print("🚀 Invoice Agent 简单演示")
    print("=" * 50)
    
    # 示例订单文本
    sample_order = """
客户姓名：王小明
订单日期：2024-01-15
联系电话：138-0000-0000

订购商品：
1. 苹果笔记本电脑 MacBook Air M2 - 2台
2. 戴尔24寸显示器 - 1台  
3. 白萝卜 - 5斤
4. 皮蛋 - 20个
5. 蒜苗 - 2斤
6. 芹菜 - 3斤

备注：请尽快发货
"""
    
    print("📄 示例订单文本:")
    print(sample_order)
    print("=" * 50)
    
    # 处理订单
    result = process_order(sample_order)
    
    print("\n✅ 处理完成！")
    print("=" * 50)
    print("📊 最终结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    print("\n🎯 匹配详情:")
    for item in result["items"]:
        status = "✅" if item["match_score"] > 0.6 else "⚠️" if item["match_score"] > 0 else "❌"
        print(f"{status} {item['original_input']} -> {item['matched_name']} (分数: {item['match_score']}, 数量: {item['quantity']}, 单价: ¥{item['unit_price']}, 小计: ¥{item['total_price']})")
    
    print(f"\n💰 订单总金额: ¥{result['total_amount']}")
    print(f"📋 订单状态: {result['status']}")
    
    print("\n🎉 演示完成！")
    print("\n💡 这个演示展示了 Invoice Agent 的核心功能：")
    print("   1. 文本信息提取")
    print("   2. 产品模糊匹配")
    print("   3. 价格计算")
    print("   4. 结构化输出")


if __name__ == "__main__":
    main()
