#!/usr/bin/env python3
"""
本地运行脚本 - 用于快速测试项目功能
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def check_python_version():
    """检查 Python 版本"""
    if sys.version_info < (3, 11):
        print("❌ 需要 Python 3.11 或更高版本")
        return False
    print(f"✅ Python 版本: {sys.version}")
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 安装 Python 依赖...")
    
    # 检查是否有 uv
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
        print("✅ 使用 uv 安装依赖")
        subprocess.run(["uv", "sync"], cwd="backend", check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  uv 未找到，使用 pip 安装依赖")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
    
    print("✅ 依赖安装完成")

def create_simple_test():
    """创建简单测试"""
    test_content = '''
"""
简单功能测试
"""
import sys
from pathlib import Path

# 添加 backend 到路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

def test_imports():
    """测试基本导入"""
    try:
        from services.fuzzy_matching_service import FuzzyMatchingService
        print("✅ 模糊匹配服务导入成功")
        
        # 测试基本功能
        service = FuzzyMatchingService()
        print("✅ 模糊匹配服务初始化成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_matching():
    """测试基本匹配功能"""
    try:
        from services.fuzzy_matching_service import FuzzyMatchingService
        
        # 模拟产品数据
        class MockProduct:
            def __init__(self, product_id, name, aliases=None):
                self.product_id = product_id
                self.name = name
                self.aliases = aliases or []
        
        products = [
            MockProduct("P001", "Apple MacBook Air M2", ["mac air", "macbook air"]),
            MockProduct("P002", "白萝卜", ["白蘿卜", "萝卜"]),
        ]
        
        service = FuzzyMatchingService()
        
        # 测试匹配
        result1 = service.find_best_match("mac air", products)
        result2 = service.find_best_match("白蘿卜", products)
        
        print(f"✅ 匹配测试 1: {result1}")
        print(f"✅ 匹配测试 2: {result2}")
        
        return True
    except Exception as e:
        print(f"❌ 匹配测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始简单功能测试...")
    
    success = True
    success &= test_imports()
    success &= test_basic_matching()
    
    if success:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败")
        sys.exit(1)
'''
    
    with open("simple_test.py", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print("✅ 创建简单测试文件")

def run_simple_test():
    """运行简单测试"""
    print("🧪 运行简单功能测试...")
    try:
        subprocess.run([sys.executable, "simple_test.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ 测试失败")
        return False

def main():
    """主函数"""
    print("🚀 Invoice Agent 本地测试启动...")
    
    # 检查 Python 版本
    if not check_python_version():
        return
    
    # 安装依赖
    try:
        install_dependencies()
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return
    
    # 创建并运行简单测试
    create_simple_test()
    
    if run_simple_test():
        print("\n🎉 基本功能测试通过！")
        print("\n📋 下一步:")
        print("1. 配置 .env 文件中的 API 密钥")
        print("2. 启动 PostgreSQL 数据库")
        print("3. 运行完整的 Docker 环境")
        print("\n💡 提示:")
        print("- 可以使用 'python backend/test_main.py' 运行更详细的测试")
        print("- 可以使用 'docker-compose up -d' 启动完整环境")
    else:
        print("\n❌ 基本功能测试失败，请检查依赖安装")

if __name__ == "__main__":
    main()
