#!/bin/bash

# Invoice Agent 启动脚本

echo "🚀 启动 Invoice Agent 系统..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，填入必要的 API 密钥"
    echo "   - OPENAI_API_KEY"
    echo "   - ANTHROPIC_API_KEY"
    echo ""
    read -p "是否继续启动？(y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p uploads
mkdir -p backend/uploads
mkdir -p n8n/workflows

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

echo ""
echo "✅ 系统启动完成！"
echo ""
echo "📋 访问地址："
echo "   前端上传界面: http://localhost:3000"
echo "   n8n 工作流:   http://localhost:5678 (admin/admin123)"
echo "   后端 API:     http://localhost:8000/docs"
echo "   数据库:       localhost:5432 (postgres/postgres123)"
echo ""
echo "📖 使用说明："
echo "   1. 访问 http://localhost:3000 上传订单文件"
echo "   2. 访问 http://localhost:5678 配置 n8n 工作流"
echo "   3. 查看 README.md 了解详细使用方法"
echo ""

# 检查健康状态
echo "🏥 检查服务健康状态..."
sleep 5

# 检查后端
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常"
fi

# 检查前端
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常"
fi

# 检查 n8n
if curl -s http://localhost:5678 > /dev/null; then
    echo "✅ n8n 服务正常"
else
    echo "❌ n8n 服务异常"
fi

echo ""
echo "🎉 Invoice Agent 系统已就绪！"
