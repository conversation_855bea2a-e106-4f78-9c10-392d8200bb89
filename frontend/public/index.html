<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Agent - 智能发票处理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background-color: #f8f9ff;
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background-color: #f0f2ff;
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #999;
            font-size: 0.9em;
        }

        #fileInput {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress {
            width: 100%;
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
            display: none;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .order-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .order-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .match-score {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .match-score.high {
            background-color: #d4edda;
            color: #155724;
        }

        .match-score.medium {
            background-color: #fff3cd;
            color: #856404;
        }

        .match-score.low {
            background-color: #f8d7da;
            color: #721c24;
        }

        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 Invoice Agent</h1>
            <p>智能发票处理系统 - 上传订单文件，自动识别并匹配产品</p>
        </div>

        <div class="content">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <div class="upload-text">点击或拖拽文件到此处上传</div>
                <div class="upload-hint">支持 PDF、JPG、PNG 等格式，最大 10MB</div>
                <input type="file" id="fileInput" accept=".pdf,.jpg,.jpeg,.png,.bmp,.tiff">
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
                <button class="btn" id="uploadBtn" onclick="uploadFile()" disabled>
                    开始处理
                </button>
            </div>

            <div class="progress" id="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在处理文件，请稍候...</p>
            </div>

            <div class="result" id="result"></div>
        </div>
    </div>

    <script>
        let selectedFile = null;

        // 文件输入处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFile = e.target.files[0];
            updateUploadArea();
        });

        // 拖拽上传
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                selectedFile = files[0];
                updateUploadArea();
            }
        });

        uploadArea.addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });

        function updateUploadArea() {
            const uploadBtn = document.getElementById('uploadBtn');
            
            if (selectedFile) {
                document.querySelector('.upload-text').textContent = `已选择: ${selectedFile.name}`;
                document.querySelector('.upload-hint').textContent = `文件大小: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`;
                uploadBtn.disabled = false;
            } else {
                document.querySelector('.upload-text').textContent = '点击或拖拽文件到此处上传';
                document.querySelector('.upload-hint').textContent = '支持 PDF、JPG、PNG 等格式，最大 10MB';
                uploadBtn.disabled = true;
            }
        }

        async function uploadFile() {
            if (!selectedFile) {
                alert('请先选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            document.getElementById('uploadBtn').disabled = true;

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showResult(result.data, 'success');
                } else {
                    showResult({ error: result.error }, 'error');
                }

            } catch (error) {
                console.error('上传错误:', error);
                showResult({ error: '网络错误，请重试' }, 'error');
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('uploadBtn').disabled = false;
            }
        }

        function showResult(data, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';

            if (type === 'success') {
                resultDiv.innerHTML = `
                    <h3>✅ 处理成功！</h3>
                    <div class="order-info">
                        <h4>订单信息</h4>
                        <p><strong>订单ID:</strong> ${data.id}</p>
                        <p><strong>客户姓名:</strong> ${data.customer_name || '未识别'}</p>
                        <p><strong>订单日期:</strong> ${data.order_date || '未识别'}</p>
                        <p><strong>状态:</strong> ${data.status}</p>
                        <p><strong>总金额:</strong> ¥${data.total_amount || 0}</p>
                        
                        <h4>订单项目</h4>
                        ${data.items.map(item => `
                            <div class="order-item">
                                <p><strong>原始输入:</strong> ${item.original_input}</p>
                                <p><strong>匹配产品:</strong> ${item.matched_name || '未匹配'}</p>
                                <p><strong>数量:</strong> ${item.quantity}</p>
                                <p><strong>匹配度:</strong> 
                                    <span class="match-score ${getScoreClass(item.match_score)}">
                                        ${(item.match_score * 100).toFixed(0)}%
                                    </span>
                                </p>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <h3>❌ 处理失败</h3>
                    <p>${data.error}</p>
                `;
            }
        }

        function getScoreClass(score) {
            if (score >= 0.8) return 'high';
            if (score >= 0.6) return 'medium';
            return 'low';
        }

        // 重置表单
        function resetForm() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            updateUploadArea();
            document.getElementById('result').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
        }
    </script>
</body>
</html>
