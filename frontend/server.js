const express = require('express');
const multer = require('multer');
const axios = require('axios');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const port = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 保持原文件名，添加时间戳避免冲突
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    cb(null, `${name}_${timestamp}${ext}`);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.bmp', '.tiff'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型'));
    }
  }
});

// 后端 API 地址
const BACKEND_URL = process.env.BACKEND_URL || 'http://backend:8000';

// 路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 文件上传处理
app.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '请选择文件' });
    }

    console.log('文件上传成功:', req.file.filename);

    // 将文件转发给后端 API
    const formData = new FormData();
    const fileBuffer = fs.readFileSync(req.file.path);
    const blob = new Blob([fileBuffer], { type: req.file.mimetype });
    formData.append('file', blob, req.file.originalname);

    const response = await axios.post(`${BACKEND_URL}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 60000 // 60秒超时
    });

    // 清理临时文件
    fs.unlinkSync(req.file.path);

    res.json({
      success: true,
      data: response.data,
      message: '文件处理成功'
    });

  } catch (error) {
    console.error('文件处理错误:', error.message);
    
    // 清理临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      error: error.response?.data?.detail || error.message || '文件处理失败'
    });
  }
});

// 获取订单详情
app.get('/order/:id', async (req, res) => {
  try {
    const response = await axios.get(`${BACKEND_URL}/orders/${req.params.id}`);
    res.json(response.data);
  } catch (error) {
    console.error('获取订单失败:', error.message);
    res.status(500).json({
      error: error.response?.data?.detail || '获取订单失败'
    });
  }
});

// 获取产品列表
app.get('/products', async (req, res) => {
  try {
    const response = await axios.get(`${BACKEND_URL}/products`);
    res.json(response.data);
  } catch (error) {
    console.error('获取产品列表失败:', error.message);
    res.status(500).json({
      error: error.response?.data?.detail || '获取产品列表失败'
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    backend_url: BACKEND_URL
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({ error: '服务器内部错误' });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`前端服务运行在 http://0.0.0.0:${port}`);
  console.log(`后端 API 地址: ${BACKEND_URL}`);
});
